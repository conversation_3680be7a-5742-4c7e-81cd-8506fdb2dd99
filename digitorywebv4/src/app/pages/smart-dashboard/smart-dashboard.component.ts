import { <PERSON>mpo<PERSON>, On<PERSON>ni<PERSON>, On<PERSON><PERSON>roy, ChangeDetector<PERSON>ef, NgZone } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';

// Angular Material Modules
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatTableModule } from '@angular/material/table';
import { MatStepperModule } from '@angular/material/stepper';
import { MatDialogModule } from '@angular/material/dialog';

// Third-party modules
import { NgChartsModule } from 'ng2-charts';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { ChartType, ChartData, ChartConfiguration } from 'chart.js';

// Services
import { SmartDashboardService } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';
import { ShareDataService } from '../../services/share-data.service';
import { DashboardConfigService, DashboardType, BaseDateOption } from '../../services/dashboard-config.service';
import { ChartRendererService, ChartModel } from '../../services/chart-renderer.service';
import { DepartmentService, Department, DepartmentCategoryMapping } from '../../services/department.service';
import { DropdownDefaultsService } from '../../services/dropdown-defaults.service';

// Components
import { DepartmentCategoryMappingComponent } from '../../components/department-category-mapping/department-category-mapping.component';
import { CategoryWorkareaMappingComponent, CategoryWorkareaMapping, WorkAreaData } from '../../components/category-workarea-mapping/category-workarea-mapping.component';

// Interfaces
interface SummaryCard {
  icon: string;
  value: string;
  label: string;
  color: string;
  data_type?: string;
}

interface DashboardMode {
  value: string;
  label: string;
  icon: string;
  disabled?: boolean;
}

@Component({
  selector: 'app-smart-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatButtonToggleModule,
    MatTooltipModule,
    MatPaginatorModule,
    MatTableModule,
    MatStepperModule,
    MatDialogModule,
    NgChartsModule,
    NgxMatSelectSearchModule,
    ReactiveFormsModule,
    FormsModule,
    DepartmentCategoryMappingComponent,
    CategoryWorkareaMappingComponent
  ],
  templateUrl: './smart-dashboard.component.html',
  styleUrls: ['./smart-dashboard.component.scss']
})
export class SmartDashboardComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  // User and branch data
  user: any;
  branches: any[] = [];
  filteredBranches: any[] = [];

  // Categories and subcategories data
  categories: any[] = [];
  subcategories: any[] = [];
  filteredCategories: any[] = [];
  filteredSubcategories: any[] = [];

  // Work areas data
  workAreas: any[] = [];
  filteredWorkAreas: any[] = [];

  // Department data
  departments: Department[] = [];
  filteredDepartments: Department[] = [];
  departmentCategoryMappings: DepartmentCategoryMapping[] = [];

  // Category-WorkArea mapping data
  categoryWorkareaMappings: CategoryWorkareaMapping[] = [];
  selectedCategoriesForMapping: string[] = [];
  showMappingConfigurationStepper = false;
  readonly selectedCategoriesForMappingCtrl = new FormControl<string[]>([]);

  // Form controls
  readonly selectedLocationsCtrl = new FormControl<string[] | string | null>([]);
  readonly locationFilterCtrl = new FormControl('');
  readonly selectedCategoriesCtrl = new FormControl<string[]>([]);
  readonly categoryFilterCtrl = new FormControl('');
  readonly selectedSubcategoriesCtrl = new FormControl<string[]>([]);
  readonly subcategoryFilterCtrl = new FormControl('');
  readonly selectedWorkAreasCtrl = new FormControl<string[]>([]);
  readonly workAreaFilterCtrl = new FormControl('');
  readonly selectedDepartmentsCtrl = new FormControl<string[]>([]);
  readonly departmentFilterCtrl = new FormControl('');
  readonly startDate = new FormControl();
  readonly endDate = new FormControl();
  readonly searchQuery = new FormControl({ value: '', disabled: true });
  readonly baseDateCtrl = new FormControl();
  readonly dashboardModeCtrl = new FormControl('default');
  selectedDashboard = '';

  // Dashboard data
  summaryCards: SummaryCard[] = [];
  charts: ChartModel[] = [];
  isLoading = false;
  isConfigLoaded = false;

  // Filter loading states
  private isBranchesLoaded = false;
  private isCategoriesLoaded = false;
  private isSubcategoriesLoaded = false;
  private isWorkAreasLoaded = false;
  private dashboardLoadTimeout: any = null;
  private isDashboardLoadPending = false;
  private isDashboardApiCallInProgress = false;

  // Dynamic configuration data
  dashboardTypes: DashboardType[] = [];
  baseDateOptions: BaseDateOption[] = [];

  // Dashboard modes
  readonly dashboardModes: DashboardMode[] = [
    { value: 'default', label: 'Default', icon: 'dashboard' },
    { value: 'ask_digi_ai', label: 'Ask Digi AI', icon: 'smart_toy', disabled: true }
  ];

  constructor(
    private readonly smartDashboardService: SmartDashboardService,
    private readonly authService: AuthService,
    private readonly shareDataService: ShareDataService,
    private readonly configService: DashboardConfigService,
    private readonly chartRenderer: ChartRendererService,
    private readonly departmentService: DepartmentService,
    private readonly dropdownDefaults: DropdownDefaultsService,
    private readonly cdr: ChangeDetectorRef,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly ngZone: NgZone
  ) {
    this.user = this.authService.getCurrentUser();
  }

  private initializeConfig(): void {
    this.configService.loadConfig().subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.configService.setConfig(response.data);
          this.setupDynamicConfigurations(response.data);
        } else {
          this.setupDefaultConfigurations();
        }
        this.isConfigLoaded = true;
        this.loadCategoriesAndSubcategories();
        if (this.selectedDashboard === 'reconciliation') {
          this.loadDepartments();
        }

        this.cdr.detectChanges();
      },
      error: () => {
        this.setupDefaultConfigurations();
        this.isConfigLoaded = true;
        this.loadCategoriesAndSubcategories();

        // Load departments if reconciliation dashboard is selected
        if (this.selectedDashboard === 'reconciliation') {
          this.loadDepartments();
        }

        this.cdr.detectChanges();
      }
    });
  }

  private setupDynamicConfigurations(config: any): void {
    // Set dashboard types - reorder to put inventory first
    const originalTypes = config.dashboard_types || [];
    this.dashboardTypes = originalTypes.sort((a: any, b: any) => {
      if (a.value === 'inventory') return -1;
      if (b.value === 'inventory') return 1;
      return 0;
    });

    // Set base date options
    this.baseDateOptions = config.base_date_options || [];

    // Check URL parameters first, then use config defaults only if backend is working
    const urlDashboard = this.route.snapshot.queryParams['dashboard'];
    const configDefault = config.ui_config?.default_dashboard_type;

    // Only set defaults when we have valid config from backend
    if (urlDashboard) {
      this.selectedDashboard = urlDashboard;
    } else if (configDefault && this.dashboardTypes.length > 0) {
      this.selectedDashboard = configDefault;
      // Update URL with backend-provided default
      this.router.navigate([], {
        relativeTo: this.route,
        queryParams: { dashboard: configDefault },
        queryParamsHandling: 'merge'
      });
    } else {
      this.selectedDashboard = '';
    }

    // Only set form defaults when backend config is available
    if (config.ui_config?.default_base_date && this.baseDateOptions.length > 0) {
      this.baseDateCtrl.setValue(config.ui_config.default_base_date);
    }



    // Only set date range if we have a valid dashboard selection
    if (this.selectedDashboard) {
      this.setDefaultDateRange();
    }
  }

  private setupDefaultConfigurations(): void {
    // No fallback configurations - keep arrays empty when backend fails
    this.dashboardTypes = [];
    this.baseDateOptions = [];

    // Only use URL parameters if available, don't set defaults
    const urlDashboard = this.route.snapshot.queryParams['dashboard'];
    if (urlDashboard) {
      this.selectedDashboard = urlDashboard;
    } else {
      this.selectedDashboard = '';
    }

    // Don't set any default values when backend is down
    // this.baseDateCtrl.setValue('deliveryDate');
    // this.setDefaultDateRange();
  }

  ngOnInit(): void {
    this.user = this.authService.getCurrentUser();

    this.initializeConfig();
    this.initializeFilters();
    // Load branches first, but don't trigger dashboard loading yet
    this.loadBranchesOnly();
    // Load existing mappings from session storage
    this.loadCategoryWorkareaMappingsFromSession();
    this.loadDepartmentCategoryMappingsFromSession();
    // Categories and subcategories will be loaded after config is ready
  }

  ngOnDestroy(): void {
    // Clear any pending dashboard load timeout
    if (this.dashboardLoadTimeout) {
      clearTimeout(this.dashboardLoadTimeout);
    }

    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeFilters(): void {
    // Location filter
    this.locationFilterCtrl.valueChanges
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((value: string | null) => {
        this.filterBranches(value || '');
      });

    // Category filter
    this.categoryFilterCtrl.valueChanges
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((value: string | null) => {
        this.filterCategories(value || '');
      });

    // Subcategory filter
    this.subcategoryFilterCtrl.valueChanges
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((value: string | null) => {
        this.filterSubcategories(value || '');
      });

    // Work area filter
    this.workAreaFilterCtrl.valueChanges
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((value: string | null) => {
        this.filterWorkAreas(value || '');
      });

    // Department filter changes
    this.departmentFilterCtrl.valueChanges
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((value: string | null) => {
        this.filterDepartments(value || '');
      });

    // Category selection for mapping changes
    this.selectedCategoriesForMappingCtrl.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe((selectedCategories: string[] | null) => {
        this.selectedCategoriesForMapping = selectedCategories || [];
      });

    // Location selection changes - reload work areas when locations are selected
    this.selectedLocationsCtrl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((selectedLocations: string[] | string | null) => {
        if (this.selectedDashboard === 'reconciliation') {
          // For reconciliation dashboard, always load work areas to show all available kitchens
          this.loadWorkAreasFromBranches();
        } else {
          // For other dashboards, only load work areas when locations are selected
          const hasSelection = selectedLocations && Array.isArray(selectedLocations) && selectedLocations.length > 0;

          if (hasSelection) {
            this.loadWorkAreasFromBranches();
          } else {
            // Clear work areas when no locations are selected
            this.workAreas = [];
            this.filteredWorkAreas = [];
            this.selectedWorkAreasCtrl.setValue([]);
            this.isWorkAreasLoaded = false;
          }
        }
      });

    // Category selection changes - reload subcategories when categories are selected
    this.selectedCategoriesCtrl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((selectedCategories: string[] | null) => {
        if (selectedCategories && selectedCategories.length > 0) {
          this.loadSubcategoriesForSelectedCategories();
        }
      });
  }

  /**
   * Load branches without triggering dashboard loading (used during initialization)
   */
  private loadBranchesOnly(): void {
    this.shareDataService.selectedBranchesSource
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.branches = data || [];
        this.filteredBranches = [...this.branches];

        // Set default selections based on dropdown type
        this.setDefaultLocationSelection();

        // Load work areas from branch data
        this.loadWorkAreasFromBranches();

        // Mark branches as loaded but don't trigger dashboard loading yet
        this.isBranchesLoaded = true;
      });
  }

  /**
   * Load work areas from user's restaurant access data
   */
  private loadWorkAreasFromBranches(): void {
    const selectedLocations = this.getLocationsAsArray();
    const workAreaData: any[] = [];

    if (this.user && this.user.restaurantAccess) {
      this.user.restaurantAccess.forEach((restaurant: any) => {
        let shouldInclude = false;

        // Handle both single selection (reconciliation) and multiple selection (other dashboards)
        if (this.selectedDashboard === 'reconciliation') {
          // For reconciliation dashboard, show all available work areas regardless of restaurant selection
          // This allows users to see all kitchen options before selecting a restaurant
          shouldInclude = true;
        } else {
          // For other dashboards, selectedLocations is an array
          shouldInclude = selectedLocations && selectedLocations.includes(restaurant.restaurantIdOld);
        }

        if (shouldInclude && restaurant.workAreas && restaurant.workAreas.length > 0) {
          workAreaData.push({
            restaurantIdOld: restaurant.restaurantIdOld,
            branchName: restaurant.branchName,
            workAreas: restaurant.workAreas,
            disabled: false
          });
        }
      });
    }

    this.workAreas = workAreaData;
    this.filteredWorkAreas = [...this.workAreas];

    // Set default work area selection based on dashboard type
    this.setDefaultWorkAreaSelection();
    this.isWorkAreasLoaded = true;
  }

  /**
   * Load departments for reconciliation dashboard
   */
  private loadDepartments(): void {
    if (this.selectedDashboard === 'reconciliation') {
      this.departmentService.getDepartments(this.user.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (departments) => {
            this.departments = departments;
            this.filteredDepartments = [...this.departments];
            this.setDefaultDepartmentSelection();
            this.loadDepartmentCategoryMappings();
            this.cdr.detectChanges();
          },
          error: () => {
            this.departments = [];
            this.filteredDepartments = [];
            this.cdr.detectChanges();
          }
        });
    }
  }

  /**
   * Load department-category mappings
   */
  private loadDepartmentCategoryMappings(): void {
    // First try to load from session storage
    this.loadDepartmentCategoryMappingsFromSession();

    // If no session storage mappings, try to load from service
    if (!this.departmentCategoryMappings || this.departmentCategoryMappings.length === 0) {
      this.departmentService.getDepartmentCategoryMappings(this.user.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (mappings) => {
            this.departmentCategoryMappings = mappings;
            // Restore department selection based on existing mappings
            this.restoreDepartmentSelectionFromMappings();
          },
          error: () => {
            this.departmentCategoryMappings = [];
            // Clear department selection if no mappings exist
            this.selectedDepartmentsCtrl.setValue([]);
          }
        });
    }
  }

  /**
   * Restore department selection based on existing mappings
   */
  private restoreDepartmentSelectionFromMappings(): void {
    if (this.departmentCategoryMappings && this.departmentCategoryMappings.length > 0) {
      // Get department IDs that have mappings configured
      const mappedDepartmentIds = this.departmentCategoryMappings.map(mapping => mapping.departmentId);

      // Filter to only include departments that exist in the current departments list
      const validDepartmentIds = mappedDepartmentIds.filter(id =>
        this.departments.some(dept => dept.id === id)
      );

      // Set the department selection to show the mapped departments
      this.selectedDepartmentsCtrl.setValue(validDepartmentIds);
    } else {
      // No mappings exist, clear selection
      this.selectedDepartmentsCtrl.setValue([]);
    }
  }

  /**
   * Check if all filter configurations are loaded and load dashboard data if ready
   */
  private checkAndLoadDashboard(): void {
    console.log('checkAndLoadDashboard called', {
      isLoading: this.isLoading,
      isDashboardLoadPending: this.isDashboardLoadPending,
      isDashboardApiCallInProgress: this.isDashboardApiCallInProgress,
      isConfigLoaded: this.isConfigLoaded,
      isBranchesLoaded: this.isBranchesLoaded,
      isCategoriesLoaded: this.isCategoriesLoaded,
      isSubcategoriesLoaded: this.isSubcategoriesLoaded,
      isWorkAreasLoaded: this.isWorkAreasLoaded,
      selectedDashboard: this.selectedDashboard
    });

    if (this.isLoading || this.isDashboardLoadPending || this.isDashboardApiCallInProgress) {
      console.log('Returning early due to loading state');
      return;
    }

    if (this.isConfigLoaded && this.isBranchesLoaded && this.isCategoriesLoaded && this.isSubcategoriesLoaded && this.isWorkAreasLoaded) {
      if (this.dashboardTypes.length > 0 && this.selectedDashboard) {
        if (this.selectedDashboard === 'reconciliation' && (!this.isReconciliationMappingConfigured())) {
          this.isLoading = false;
          this.clearDashboardData();
          this.showMappingRequiredMessage();
          this.cdr.detectChanges();
        } else {
          console.log('Calling loadDashboardData from checkAndLoadDashboard');
          this.loadDashboardData();
        }
      } else {
        this.isLoading = false;
        this.clearDashboardData();
      }
    }
  }

  /**
   * Check if all filters are loaded (used for UI state management)
   */
  get areAllFiltersLoaded(): boolean {
    return this.isConfigLoaded && this.isBranchesLoaded && this.isCategoriesLoaded && this.isSubcategoriesLoaded && this.isWorkAreasLoaded;
  }

  /**
   * Check if mapping-related filters are loaded (used for mapping button state)
   * Mapping buttons don't need work areas to be loaded, only basic config and categories
   */
  get areMappingFiltersLoaded(): boolean {
    const isLoaded = this.isConfigLoaded && this.isBranchesLoaded && this.isCategoriesLoaded;

    // Debug logging for troubleshooting button disable issues
    if (!isLoaded) {
      console.debug('Mapping buttons disabled - Loading states:', {
        isConfigLoaded: this.isConfigLoaded,
        isBranchesLoaded: this.isBranchesLoaded,
        isCategoriesLoaded: this.isCategoriesLoaded
      });
    }

    return isLoaded;
  }

  /**
   * Reset filter loading states (used when dashboard type changes)
   */
  private resetFilterLoadingStates(): void {
    this.isCategoriesLoaded = false;
    this.isSubcategoriesLoaded = false;
    // Note: branches don't need to be reloaded when dashboard type changes
  }

  /**
   * Schedule dashboard loading with debouncing to prevent multiple simultaneous calls
   */
  private scheduleDashboardLoad(): void {
    console.log('scheduleDashboardLoad called', {
      isDashboardLoadPending: this.isDashboardLoadPending,
      isLoading: this.isLoading,
      isDashboardApiCallInProgress: this.isDashboardApiCallInProgress,
      hasExistingTimeout: !!this.dashboardLoadTimeout
    });

    // Don't schedule if API call is already in progress
    if (this.isDashboardApiCallInProgress || this.isLoading) {
      console.log('Skipping scheduleDashboardLoad - API call in progress');
      return;
    }

    // Clear any existing timeout
    if (this.dashboardLoadTimeout) {
      console.log('Clearing existing timeout');
      clearTimeout(this.dashboardLoadTimeout);
    }

    // Set a flag to indicate a load is pending
    this.isDashboardLoadPending = true;

    // Schedule the load with a small delay to allow form controls to settle
    this.dashboardLoadTimeout = setTimeout(() => {
      console.log('Timeout executed, calling checkAndLoadDashboard');
      this.isDashboardLoadPending = false;
      this.checkAndLoadDashboard();
    }, 100);
  }

  private filterBranches(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredBranches = [...this.branches];
    } else {
      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\s/g, '');
      this.filteredBranches = this.branches.filter(branch =>
        branch.branchName.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm)
      );
    }
  }

  private loadCategoriesAndSubcategories(): void {
    if (!this.user?.tenantId) {
      return;
    }

    // Load categories first, then subcategories with all categories
    this.smartDashboardService.getCategories(this.user.tenantId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (categoriesResponse) => {
          // Process categories - handle both object and array response structures
          if (categoriesResponse && (categoriesResponse.success || categoriesResponse.result === 'success') && categoriesResponse.categories) {
            let categoryKeys = [];

            // Check if categories is an array or object
            if (Array.isArray(categoriesResponse.categories)) {
              categoryKeys = categoriesResponse.categories;
            } else if (typeof categoriesResponse.categories === 'object') {
              categoryKeys = Object.keys(categoriesResponse.categories);
            }

            // Force new array references to trigger change detection
            this.categories = [...categoryKeys];
            this.filteredCategories = [...categoryKeys];

            // Set default category selection
            this.setDefaultCategorySelection();

            // Immediately trigger change detection
            this.cdr.markForCheck();
            this.cdr.detectChanges();

            // Now load subcategories with all categories (or empty array if no categories)
            const categoriesToSend = this.categories.length > 0 ? this.categories : [];
            this.smartDashboardService.getSubCategories(this.user.tenantId, categoriesToSend)
              .pipe(takeUntil(this.destroy$))
              .subscribe({
                next: (subcategoriesResponse) => {
                  // Process subcategories
                  if (subcategoriesResponse && (subcategoriesResponse.success || subcategoriesResponse.result === 'success') && subcategoriesResponse.subcategories) {
                    let allSubcategories = [];

                    // Check if subcategories is an array or object
                    if (Array.isArray(subcategoriesResponse.subcategories)) {
                      allSubcategories = subcategoriesResponse.subcategories;
                    } else if (typeof subcategoriesResponse.subcategories === 'object') {
                      Object.values(subcategoriesResponse.subcategories).forEach((categorySubcats: any) => {
                        if (Array.isArray(categorySubcats)) {
                          allSubcategories.push(...categorySubcats);
                        }
                      });
                    }

                    // Force new array references to trigger change detection
                    const uniqueSubcategories = [...new Set(allSubcategories)];
                    this.subcategories = [...uniqueSubcategories];
                    this.filteredSubcategories = [...uniqueSubcategories];
                    this.setDefaultSubcategorySelection();

                    this.ngZone.run(() => {
                      this.cdr.detectChanges();
                    });
                  } else {
                    this.subcategories = [];
                    this.filteredSubcategories = [];
                  }
                  this.isCategoriesLoaded = true;
                  this.isSubcategoriesLoaded = true;
                  this.scheduleDashboardLoad();
                },
                error: () => {
                  this.subcategories = [];
                  this.filteredSubcategories = [];
                  this.isCategoriesLoaded = true;
                  this.isSubcategoriesLoaded = true;
                  this.scheduleDashboardLoad();
                }
              });
          } else {
            this.categories = [];
            this.filteredCategories = [];

            this.smartDashboardService.getSubCategories(this.user.tenantId, [])
              .pipe(takeUntil(this.destroy$))
              .subscribe({
                next: (subcategoriesResponse) => {
                  if (subcategoriesResponse && (subcategoriesResponse.success || subcategoriesResponse.result === 'success') && subcategoriesResponse.subcategories) {
                    let allSubcategories = [];

                    if (Array.isArray(subcategoriesResponse.subcategories)) {
                      allSubcategories = subcategoriesResponse.subcategories;
                    } else if (typeof subcategoriesResponse.subcategories === 'object') {
                      Object.values(subcategoriesResponse.subcategories).forEach((categorySubcats: any) => {
                        if (Array.isArray(categorySubcats)) {
                          allSubcategories.push(...categorySubcats);
                        }
                      });
                    }

                    this.subcategories = [...new Set(allSubcategories)];
                    this.filteredSubcategories = [...this.subcategories];
                    this.setDefaultSubcategorySelection();
                  } else {
                    this.subcategories = [];
                    this.filteredSubcategories = [];
                  }

                  this.isCategoriesLoaded = true;
                  this.isSubcategoriesLoaded = true;
                  this.cdr.detectChanges();
                  this.scheduleDashboardLoad();
                },
                error: () => {
                  this.subcategories = [];
                  this.filteredSubcategories = [];
                  this.isCategoriesLoaded = true;
                  this.isSubcategoriesLoaded = true;
                  this.scheduleDashboardLoad();
                }
              });
          }
        },
        error: () => {
          this.categories = [];
          this.filteredCategories = [];
          this.subcategories = [];
          this.filteredSubcategories = [];
          // Mark as loaded even on error to prevent infinite waiting
          this.isCategoriesLoaded = true;
          this.isSubcategoriesLoaded = true;

          // Schedule dashboard loading with debouncing
          this.scheduleDashboardLoad();
        }
      });
  }

  private loadSubcategoriesForSelectedCategories(): void {
    if (!this.user?.tenantId) {
      return;
    }

    // Reset subcategories loading state when categories change
    this.isSubcategoriesLoaded = false;

    // Get selected categories
    const selectedCategories = this.selectedCategoriesCtrl.value || [];

    this.smartDashboardService.getSubCategories(this.user.tenantId, selectedCategories)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response['result'] == 'success') {
            const allSubcategories = response.subCategories;
            this.subcategories = [...new Set(allSubcategories)];
            this.filteredSubcategories = [...this.subcategories];

            // Set default subcategory selection
            this.setDefaultSubcategorySelection();

            // Mark subcategories as loaded
            this.isSubcategoriesLoaded = true;

            // Trigger change detection
            this.cdr.detectChanges();

            // Schedule dashboard loading with debouncing
            this.scheduleDashboardLoad();
          } else {
            this.subcategories = [];
            this.filteredSubcategories = [];
            // Mark as loaded even on error to prevent infinite waiting
            this.isSubcategoriesLoaded = true;

            // Schedule dashboard loading with debouncing
            this.scheduleDashboardLoad();
          }
        },
        error: () => {
          this.subcategories = [];
          this.filteredSubcategories = [];
          // Mark as loaded even on error to prevent infinite waiting
          this.isSubcategoriesLoaded = true;

          // Schedule dashboard loading with debouncing
          this.scheduleDashboardLoad();
        }
      });
  }

  private filterCategories(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredCategories = [...this.categories];
    } else {
      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\s/g, '');
      this.filteredCategories = this.categories.filter(category =>
        category.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm)
      );
    }
  }

  private filterSubcategories(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredSubcategories = [...this.subcategories];
    } else {
      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\s/g, '');
      this.filteredSubcategories = this.subcategories.filter(subcategory =>
        subcategory.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm)
      );
    }
  }

  private filterWorkAreas(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredWorkAreas = [...this.workAreas];
    } else {
      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\s/g, '');
      this.filteredWorkAreas = this.workAreas.map(branch => ({
        ...branch,
        workAreas: branch.workAreas.filter((workArea: string) =>
          workArea.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm)
        )
      })).filter(branch => branch.workAreas.length > 0);
    }
  }

  private filterDepartments(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredDepartments = [...this.departments];
    } else {
      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\s/g, '');
      this.filteredDepartments = this.departments.filter(department =>
        department.name.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm) ||
        (department.code && department.code.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm))
      );
    }
  }

  // Track by function for ngFor performance
  trackByIndex(index: number): number {
    return index;
  }

  // Helper method for work areas count
  getTotalWorkAreasCount(): number {
    if (!this.filteredWorkAreas || this.filteredWorkAreas.length === 0) {
      return 0;
    }
    return this.filteredWorkAreas.flatMap(branch => branch.workAreas || []).length;
  }

  // Default selection methods for dropdowns
  private setDefaultLocationSelection(): void {
    const isMultiSelect = this.selectedDashboard !== 'reconciliation';
    this.dropdownDefaults.setDefaultLocationSelection(
      this.selectedLocationsCtrl,
      this.branches,
      isMultiSelect
    );
  }

  private setDefaultCategorySelection(): void {
    this.dropdownDefaults.setDefaultCategorySelection(
      this.selectedCategoriesCtrl,
      this.categories
    );
  }

  private setDefaultSubcategorySelection(): void {
    this.dropdownDefaults.setDefaultCategorySelection(
      this.selectedSubcategoriesCtrl,
      this.subcategories
    );
    this.cdr.detectChanges();
  }

  private setDefaultWorkAreaSelection(): void {
    if (this.selectedDashboard === 'reconciliation') {
      this.selectedWorkAreasCtrl.setValue([]);
    } else {
      this.dropdownDefaults.setDefaultWorkAreaSelection(
        this.selectedWorkAreasCtrl,
        this.workAreas,
        true,
        true
      );
    }
  }

  private setDefaultDepartmentSelection(): void {
    this.dropdownDefaults.setDefaultDepartmentSelection(
      this.selectedDepartmentsCtrl,
      this.departments
    );
  }

  // Select All / Deselect All methods for Restaurants
  areAllRestaurantsSelected(): boolean {
    const selectedLocations = this.getLocationsAsArray();
    const selectedCount = selectedLocations.length;
    const totalCount = this.filteredBranches.length;
    return selectedCount === totalCount && totalCount > 0;
  }

  toggleAllRestaurants(event?: Event): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (this.areAllRestaurantsSelected()) {
      this.selectedLocationsCtrl.setValue([]);
    } else {
      const allRestaurantIds = this.filteredBranches.map(branch => branch.restaurantIdOld);
      this.selectedLocationsCtrl.setValue(allRestaurantIds);
    }
  }

  // Select All / Deselect All methods for Categories
  areAllCategoriesSelected(): boolean {
    const selectedCount = this.selectedCategoriesCtrl.value?.length || 0;
    const totalCount = this.filteredCategories.length;
    return selectedCount === totalCount && totalCount > 0;
  }

  toggleAllCategories(event?: Event): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (this.areAllCategoriesSelected()) {
      this.selectedCategoriesCtrl.setValue([]);
    } else {
      this.selectedCategoriesCtrl.setValue([...this.filteredCategories]);
    }
  }

  // Select All / Deselect All methods for Subcategories
  areAllSubcategoriesSelected(): boolean {
    const selectedCount = this.selectedSubcategoriesCtrl.value?.length || 0;
    const totalCount = this.filteredSubcategories.length;
    return selectedCount === totalCount && totalCount > 0;
  }

  toggleAllSubcategories(event?: Event): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (this.areAllSubcategoriesSelected()) {
      this.selectedSubcategoriesCtrl.setValue([]);
    } else {
      this.selectedSubcategoriesCtrl.setValue([...this.filteredSubcategories]);
    }
  }

  // Select All / Deselect All methods for Work Areas
  areAllWorkAreasSelected(): boolean {
    const selectedCount = this.selectedWorkAreasCtrl.value?.length || 0;
    const totalCount = this.getTotalWorkAreasCount();
    return selectedCount === totalCount && totalCount > 0;
  }

  toggleAllWorkAreas(event?: Event): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (this.areAllWorkAreasSelected()) {
      this.selectedWorkAreasCtrl.setValue([]);
    } else {
      const allWorkAreas = this.filteredWorkAreas.flatMap(branch => branch.workAreas || []);
      this.selectedWorkAreasCtrl.setValue(allWorkAreas);
    }
  }

  // Select All / Deselect All methods for Departments
  areAllDepartmentsSelected(): boolean {
    const selectedCount = this.selectedDepartmentsCtrl.value?.length || 0;
    const totalCount = this.filteredDepartments.length;
    return selectedCount === totalCount && totalCount > 0;
  }

  toggleAllDepartments(event?: Event): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (this.areAllDepartmentsSelected()) {
      this.selectedDepartmentsCtrl.setValue([]);
    } else {
      const allDepartmentIds = this.filteredDepartments.map(dept => dept.id);
      this.selectedDepartmentsCtrl.setValue(allDepartmentIds);
    }
  }



  // Combined Mapping Configuration Stepper methods
  openMappingConfiguration(): void {
    // Load existing mappings from session storage
    this.loadCategoryWorkareaMappingsFromSession();
    this.loadDepartmentCategoryMappingsFromSession();

    // If there are existing category mappings, populate the selected categories
    if (this.categoryWorkareaMappings && this.categoryWorkareaMappings.length > 0) {
      const existingCategories = this.categoryWorkareaMappings.map(mapping => mapping.categoryName);
      this.selectedCategoriesForMappingCtrl.setValue(existingCategories);
    } else {
      // Start with no categories selected if no existing mappings
      this.selectedCategoriesForMappingCtrl.setValue([]);
    }

    this.showMappingConfigurationStepper = true;
  }

  closeMappingConfiguration(): void {
    this.showMappingConfigurationStepper = false;
  }

  completeMappingConfiguration(): void {
    this.showMappingConfigurationStepper = false;
    // Optionally show a success message or trigger dashboard refresh
  }

  onDepartmentMappingsChanged(mappings: DepartmentCategoryMapping[]): void {
    this.departmentCategoryMappings = mappings;
    // Save mappings to session storage
    this.saveDepartmentCategoryMappingsToSession(mappings);
    // Don't close the stepper here - let user complete the flow
  }

  onCategoryWorkareaMappingsChanged(mappings: CategoryWorkareaMapping[]): void {
    this.categoryWorkareaMappings = mappings;
    // Save mappings to session storage
    this.saveCategoryWorkareaMappingsToSession(mappings);
    // Update work areas selection based on mappings
    this.updateWorkAreasFromMappings(mappings);
    // Don't close the stepper here - let user complete the flow
  }

  isMappingConfigurationAvailable(): boolean {
    return this.areMappingFiltersLoaded;
  }



  // Category selection methods for category-workarea mapping dialog
  areAllCategoriesSelectedForMapping(): boolean {
    const selectedCount = this.selectedCategoriesForMappingCtrl.value?.length || 0;
    const totalCount = this.filteredCategories.length;
    return selectedCount === totalCount && totalCount > 0;
  }

  toggleAllCategoriesForMapping(event?: Event): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (this.areAllCategoriesSelectedForMapping()) {
      this.selectedCategoriesForMappingCtrl.setValue([]);
    } else {
      this.selectedCategoriesForMappingCtrl.setValue([...this.filteredCategories]);
    }
  }

  trackByCategory(_index: number, category: string): string {
    return category;
  }

  // Session storage methods for category-workarea mappings
  private saveCategoryWorkareaMappingsToSession(mappings: CategoryWorkareaMapping[]): void {
    if (this.user?.tenantId) {
      const key = `categoryWorkareaMappings_${this.user.tenantId}`;
      sessionStorage.setItem(key, JSON.stringify(mappings));
    }
  }

  private loadCategoryWorkareaMappingsFromSession(): void {
    if (this.user?.tenantId) {
      const key = `categoryWorkareaMappings_${this.user.tenantId}`;
      const stored = sessionStorage.getItem(key);
      if (stored) {
        try {
          this.categoryWorkareaMappings = JSON.parse(stored);
        } catch (error) {
          console.warn('Failed to parse stored category-workarea mappings:', error);
          this.categoryWorkareaMappings = [];
        }
      }
    }
  }

  // Session storage methods for department-category mappings
  private saveDepartmentCategoryMappingsToSession(mappings: DepartmentCategoryMapping[]): void {
    if (this.user?.tenantId) {
      const key = `departmentCategoryMappings_${this.user.tenantId}`;
      sessionStorage.setItem(key, JSON.stringify(mappings));
    }
  }

  private loadDepartmentCategoryMappingsFromSession(): void {
    if (this.user?.tenantId) {
      const key = `departmentCategoryMappings_${this.user.tenantId}`;
      const stored = sessionStorage.getItem(key);
      if (stored) {
        try {
          this.departmentCategoryMappings = JSON.parse(stored);
          // Restore department selection based on loaded mappings
          this.restoreDepartmentSelectionFromMappings();
        } catch (error) {
          console.warn('Failed to parse stored department-category mappings:', error);
          this.departmentCategoryMappings = [];
        }
      }
    }
  }

  private updateWorkAreasFromMappings(mappings: CategoryWorkareaMapping[]): void {
    // Get all work areas from the mappings
    const mappedWorkAreas = new Set<string>();
    mappings.forEach(mapping => {
      mapping.workAreas.forEach(workArea => {
        mappedWorkAreas.add(workArea);
      });
    });

    // Update the selected work areas control
    this.selectedWorkAreasCtrl.setValue(Array.from(mappedWorkAreas));
  }

  /**
   * Check if category-workarea mapping is configured
   * Always returns true since mapping is now optional
   */
  isCategoryWorkareaMappingConfigured(): boolean {
    return true; // Category-workarea mapping is now optional
  }

  /**
   * Check if department-category mapping is configured
   */
  isDepartmentCategoryMappingConfigured(): boolean {
    if (!this.departmentCategoryMappings || this.departmentCategoryMappings.length === 0) {
      return false;
    }

    // Check if at least one mapping has categories assigned
    const hasValidMappings = this.departmentCategoryMappings.some(mapping =>
      mapping.categories && mapping.categories.length > 0
    );

    return hasValidMappings;
  }



  /**
   * Get work areas data formatted for the category-workarea mapping component
   */
  getWorkAreasForMapping(): WorkAreaData[] {
    return this.workAreas.map(workArea => ({
      restaurantIdOld: workArea.restaurantIdOld,
      branchName: workArea.branchName,
      workAreas: workArea.workAreas,
      disabled: workArea.disabled || false
    }));
  }

  /**
   * Check if reconciliation dashboard has required department-category mappings configured
   */
  isReconciliationMappingConfigured(): boolean {
    if (this.selectedDashboard !== 'reconciliation') {
      return true; // Not applicable for other dashboards
    }

    // Check if we have any mappings configured
    if (!this.departmentCategoryMappings || this.departmentCategoryMappings.length === 0) {
      return false;
    }

    if (!this.categoryWorkareaMappings || this.categoryWorkareaMappings.length === 0) {
      return false;
    }

    return true;
  }


  /**
   * Show message to user that configuration is required
   */
  private showMappingRequiredMessage(): void {
    // Force the warning message to be visible by triggering change detection
    this.cdr.detectChanges();

    // Optional: Auto-scroll to the warning message
    setTimeout(() => {
      const warningElement = document.querySelector('.mapping-warning-container');
      if (warningElement) {
        warningElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }, 100);
  }

  loadDashboardData(): void {
    console.log('loadDashboardData called');
    this.loadDashboardDataInternal();
  }

  private clearDashboardData(): void {
    this.summaryCards = [];
    this.charts = [];
  }

  private processDashboardData(data: any): void {
    // Process summary cards using smart dashboard service with Indian formatting
    this.summaryCards = data.summary_items?.map((item: any) => ({
      icon: item.icon || this.smartDashboardService.getSummaryCardIcon(item.data_type, item.label),
      value: this.formatCardValue(item.value, item.data_type),
      label: item.label,
      color: this.configService.getContextualColor(0, item.data_type),
      data_type: item.data_type
    })) || [];

    // Process charts using chart renderer service
    this.charts = data.charts?.map((chart: any) => {
      const processedChart = this.chartRenderer.processChart(chart);
      return processedChart;
    }) || [];
  }

  /**
   * Format card value based on data type with Indian numbering
   */
  private formatCardValue(value: any, dataType?: string): string {
    // Handle non-numeric values
    if (value === null || value === undefined || value === '') {
      return '0';
    }

    // If it's already a formatted string (contains letters), return as is
    if (typeof value === 'string' && /[a-zA-Z]/.test(value)) {
      return value;
    }

    // Convert to number for formatting
    const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^\d.-]/g, '')) : value;

    if (isNaN(numValue)) {
      return value.toString();
    }

    // Format based on data type
    if (dataType && (dataType.toLowerCase().includes('currency') || dataType.toLowerCase().includes('amount') || dataType.toLowerCase().includes('cost') || dataType.toLowerCase().includes('price'))) {
      return this.smartDashboardService.formatCurrency(numValue);
    } else {
      return this.smartDashboardService.formatExactNumber(numValue);
    }
  }

  private getLocationsAsArray(): string[] {
    const value = this.selectedLocationsCtrl.value;
    if (!value) return [];
    if (Array.isArray(value)) return value;
    return [value]; // Convert single string to array
  }

  private formatDate(date: Date): string {
    // Fix the date offset issue by using local date components
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  private setDefaultDateRange(): void {
    // Only set date range if we have valid configuration and dashboard selection
    if (!this.selectedDashboard || this.dashboardTypes.length === 0) {
      return;
    }

    const today = new Date();
    // For both dashboards: current month start to current date
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    this.startDate.setValue(startOfMonth);
    this.endDate.setValue(today);
  }

  onLocationChange(): void {
    // No automatic API call - user must click Search button
  }

  onDateChange(): void {
    // No automatic API call - user must click Search button
  }

  onDashboardChange(): void {
    // Clear right side content immediately when dashboard type changes
    this.clearDashboardData();
    this.isLoading = false;

    // Update URL parameter when dashboard changes
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { dashboard: this.selectedDashboard },
      queryParamsHandling: 'merge'
    });

    // Update date range when dashboard type changes
    this.setDefaultDateRange();

    // Reset filter loading states since we're reloading categories/subcategories
    this.resetFilterLoadingStates();

    // Reload categories and subcategories for the new dashboard type
    this.loadCategoriesAndSubcategories();

    // Reset selections using default selection methods
    this.setDefaultLocationSelection();
    this.setDefaultWorkAreaSelection();

    if (this.selectedDashboard === 'reconciliation') {
      this.loadDepartments();
    } else {
      // Clear department selection for non-reconciliation dashboards
      this.selectedDepartmentsCtrl.setValue([]);
    }

    // Trigger change detection to update the UI immediately
    this.cdr.detectChanges();
  }

  searchDashboard(): void {
    // This method is called when Search button is clicked
    console.log('searchDashboard called');
    this.loadDashboardData();
  }

  resetFilters(): void {
    // Reset filters using default selection methods
    this.setDefaultLocationSelection();
    this.setDefaultCategorySelection();
    this.setDefaultSubcategorySelection();
    this.setDefaultWorkAreaSelection();
    this.setDefaultDepartmentSelection();

    // Load category-workarea mappings from session (don't reset them)
    this.loadCategoryWorkareaMappingsFromSession();

    // Only set defaults if we have valid configuration
    if (this.selectedDashboard) {
      this.setDefaultDateRange();
    }

    // Only set base date default if we have options available
    if (this.baseDateOptions.length > 0) {
      const defaultBaseDate = this.baseDateOptions[0]?.value;
      if (defaultBaseDate) {
        this.baseDateCtrl.setValue(defaultBaseDate);
      }
    }

    this.searchQuery.setValue('');
    this.locationFilterCtrl.setValue('');
    this.categoryFilterCtrl.setValue('');
    this.subcategoryFilterCtrl.setValue('');
    this.workAreaFilterCtrl.setValue('');
    this.filteredBranches = [...this.branches];
    this.filteredCategories = [...this.categories];
    this.filteredSubcategories = [...this.subcategories];
    this.filteredWorkAreas = [...this.workAreas];

    // Load dashboard data with reset filters
    this.loadDashboardData();
  }

  onSearchQuery(): void {
    const query = this.searchQuery.value?.trim();
    if (query) {
      this.loadDashboardDataWithQuery(query);
    } else {
      this.loadDashboardData();
    }
  }

  private loadDashboardDataWithQuery(query: string): void {
    this.loadDashboardDataInternal(query, false);
  }

  private loadDashboardDataInternal(userQuery: string = '', useDefaultCharts: boolean = true): void {
    console.log('loadDashboardDataInternal called with:', {
      userQuery,
      useDefaultCharts,
      isLoading: this.isLoading,
      isDashboardApiCallInProgress: this.isDashboardApiCallInProgress
    });

    if (this.isLoading || this.isDashboardApiCallInProgress) {
      console.log('Already loading or API call in progress, returning early');
      return;
    }

    if (!this.startDate.value || !this.endDate.value || !this.selectedDashboard) {
      this.isLoading = false;
      this.clearDashboardData();
      this.cdr.detectChanges();
      return;
    }

    if (this.selectedDashboard === 'reconciliation' && (!this.isReconciliationMappingConfigured())) {
      this.isLoading = false;
      this.clearDashboardData();
      this.showMappingRequiredMessage();
      this.cdr.detectChanges();
      return;
    }

    this.isLoading = true;
    this.isDashboardApiCallInProgress = true;
    console.log('Making API call to getSmartDashboardData');

    const selectedCategories = this.selectedCategoriesCtrl.value || [];
    const selectedSubcategories = this.selectedSubcategoriesCtrl.value || [];
    const selectedWorkAreas = this.selectedWorkAreasCtrl.value || [];
    const selectedDepartments = this.selectedDepartmentsCtrl.value || [];

    const filters = {
      locations: this.getLocationsAsArray(),
      startDate: this.formatDate(this.startDate.value),
      endDate: this.formatDate(this.endDate.value),
      baseDate: this.baseDateCtrl.value || 'deliveryDate',
      categories: selectedCategories.length > 0 ? selectedCategories : undefined,
      subcategories: selectedSubcategories.length > 0 ? selectedSubcategories : undefined,
      workAreas: selectedWorkAreas.length > 0 ? selectedWorkAreas : undefined,
      departments: selectedDepartments.length > 0 ? selectedDepartments : undefined
    };

    const request = {
      tenant_id: this.user.tenantId,
      filters: filters,
      user_query: userQuery,
      use_default_charts: useDefaultCharts,
      dashboard_type: this.selectedDashboard,
      department_category_mappings: this.selectedDashboard === 'reconciliation' ? this.departmentCategoryMappings : undefined,
      category_workarea_mappings: this.selectedDashboard === 'reconciliation' ? this.categoryWorkareaMappings : undefined
    };

    this.smartDashboardService.getSmartDashboardData(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('API response received');
          if (response.status === 'success') {
            this.processDashboardData(response.data);
          } else {
            this.clearDashboardData();
            // Handle specific error cases for reconciliation dashboard
            if (response.error_code === 'MAPPING_REQUIRED' || response.error_code === 'INVALID_MAPPING') {
              this.showMappingRequiredMessage();
            }
          }
          this.isLoading = false;
          this.isDashboardApiCallInProgress = false;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.log('API error received');
          this.clearDashboardData();
          // Check if it's a mapping-related error
          if (error.error && (error.error.error_code === 'MAPPING_REQUIRED' || error.error.error_code === 'INVALID_MAPPING')) {
            this.showMappingRequiredMessage();
          }
          this.isLoading = false;
          this.isDashboardApiCallInProgress = false;
          this.cdr.detectChanges();
        }
      });
  }

  // Dynamic chart methods using services
  getChartData(chart: ChartModel): ChartData {
    return chart.data;
  }

  getChartType(chart: ChartModel): ChartType {
    return this.chartRenderer.getChartType(chart.type);
  }

  getChartOptions(chart: ChartModel): ChartConfiguration['options'] {
    return chart.options || this.configService.getDefaultChartOptions();
  }

  getChartCssClass(chart: ChartModel): string {
    return this.chartRenderer.getChartCssClass(chart);
  }

  // Enhanced helper methods for table charts
  getTableHeaders(chart: ChartModel): string[] {
    return (chart.data as any)?.headers || [];
  }

  getTableRows(chart: ChartModel): any[] {
    return (chart.data as any)?.rows || [];
  }

  getTableOptions(chart: ChartModel): any {
    return (chart as any)?.options || {};
  }

  // Material table support methods
  getTableDataSource(chart: ChartModel): any[] {
    return this.getFilteredTableRows(chart);
  }

  getDisplayedColumns(chart: ChartModel): string[] {
    const headers = this.getTableHeaders(chart);
    return headers.map((_, index) => 'col' + index);
  }

  // Table filtering and search
  private tableFilters: { [chartId: string]: string } = {};

  getFilteredTableRows(chart: ChartModel): any[] {
    const rows = this.getTableRows(chart);
    const filter = this.tableFilters[chart.id];

    if (!filter || filter.trim() === '') {
      return rows;
    }

    const filterLower = filter.toLowerCase();
    return rows.filter(row => {
      return Object.values(row).some(value =>
        value && value.toString().toLowerCase().includes(filterLower)
      );
    });
  }

  applyTableFilter(event: Event, chart: ChartModel): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.tableFilters[chart.id] = filterValue;
  }

  // Table styling methods - REMOVED INLINE STYLING
  // All styling is now handled by CSS classes for better performance and consistency

  formatTableCell(value: any, header: string, row: any): string {
    if (value === null || value === undefined || value === '' || value === '-') {
      return '<span class="empty-cell">-</span>';
    }

    if (header === 'Expense Head/Item' && typeof value === 'string' && value.startsWith('  ')) {
      return `<span class="subcategory-item">${value}</span>`;
    }

    // Handle special row types with bold formatting
    if (row && (row._isDepartmentSales || row._isDepartmentTotal || row._isOverallSales ||
      row._isOverallTotal || row._isGrandTotal || row._isCategoryRow)) {
      return `<strong>${value}</strong>`;
    }

    // Handle currency columns (headers contain ₹)
    if (typeof header === 'string' && header.includes('(₹)') && typeof value === 'string') {
      // Check if value is numeric (with commas)
      if (/^[\d,]+(\.\d+)?$/.test(value.replace(/,/g, ''))) {
        return `<span class="currency-cell">${value}</span>`;
      }
    }

    // Handle percentage columns (headers contain %)
    if (typeof header === 'string' && header.includes('%') && typeof value === 'string') {
      // Check if value is numeric
      if (/^\d+(\.\d+)?$/.test(value)) {
        return `<span class="percentage-cell">${value}%</span>`;
      }
    }

    return value;
  }

  // Export functionality
  exportTable(chart: ChartModel): void {
    const headers = this.getTableHeaders(chart);
    const rows = this.getFilteredTableRows(chart);

    // Create CSV content
    const csvContent = [
      headers.join(','),
      ...rows.map(row =>
        headers.map(header => {
          let value = row[header] || '';
          // Clean up formatting for CSV
          value = value.toString().replace(/\*\*/g, '').replace(/<[^>]*>/g, '');
          // Escape commas and quotes
          if (value.includes(',') || value.includes('"')) {
            value = `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        }).join(',')
      )
    ].join('\n');

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${chart.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  // Dashboard mode methods
  isSearchBarDisabled(): boolean {
    return this.dashboardModeCtrl.value === 'default' || this.isAiModeDisabled();
  }

  isAiModeDisabled(): boolean {
    return this.dashboardModes.find(mode => mode.value === 'ask_digi_ai')?.disabled || false;
  }

  setDashboardMode(mode: string): void {
    if (mode === 'ask_digi_ai' && this.isAiModeDisabled()) {
      // Don't allow switching to AI mode if it's disabled
      return;
    }
    this.dashboardModeCtrl.setValue(mode);
  }

  // Expose window object for template access
  window = window;
}
